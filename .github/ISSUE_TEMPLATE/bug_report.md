---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

## Bug Description
A clear and concise description of what the bug is.

## Steps to Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Screenshots
If applicable, add screenshots to help explain your problem.

## Environment Information
**WordPress Environment:**
- WordPress Version: [e.g. 6.4]
- Plugin Version: [e.g. 1.0.0]
- PHP Version: [e.g. 8.1]
- MySQL Version: [e.g. 8.0]

**Server Environment:**
- Server OS: [e.g. Ubuntu 20.04]
- Web Server: [e.g. Apache 2.4, Nginx 1.18]
- Hosting Provider: [e.g. SiteGround, WP Engine]

**Browser Information (if applicable):**
- Browser: [e.g. Chrome, Firefox, Safari]
- Version: [e.g. 22]
- Device: [e.g. Desktop, Mobile]

## Active Plugins
List other active plugins that might be relevant:
- Plugin Name (Version)
- Plugin Name (Version)

## Error Logs
If applicable, paste any relevant error logs here:
```
Paste error logs here
```

## Additional Context
Add any other context about the problem here.

## Possible Solution
If you have any ideas about what might be causing the issue or how to fix it, please share them here.

## Checklist
- [ ] I have searched for existing issues before creating this one
- [ ] I have provided all the requested information
- [ ] I have tested this with only this plugin active (if possible)
- [ ] I have checked the error logs
