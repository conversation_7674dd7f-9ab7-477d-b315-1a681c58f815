@charset "UTF-8";
/*!
 * All theme styles should be placed in this file.
 */
/* .nav-id-193,
.nav-id-199,
.nav-id-201 {
  display: none;
} */

/* -------------------------------------------------------------------------- */
/* Override theme styles here                                      */
/* -------------------------------------------------------------------------- */
.text-muted {
    color: #9fa6bc !important
}

/* -------------------------------------------------------------------------- */
/* Timeline Scrollbar Styles                                                  */
/* -------------------------------------------------------------------------- */
.timeline-scroll-wrapper {
    scrollbar-width: thin;
    scrollbar-color: #e5e7eb #f3f4f6;
}

.timeline-scroll-wrapper::-webkit-scrollbar {
    width: 8px;
}

.timeline-scroll-wrapper::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 4px;
}

.timeline-scroll-wrapper::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.timeline-scroll-wrapper::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Smooth scroll behavior */
.timeline-scroll-wrapper {
    scroll-behavior: smooth;
}

/* Add subtle shadow at top/bottom when scrolling */
.timeline-scroll-wrapper {
    position: relative;
    background:
        linear-gradient(white 30%, rgba(255,255,255,0)),
        linear-gradient(rgba(255,255,255,0), white 70%) 0 100%,
        radial-gradient(farthest-side at 50% 0, rgba(0,0,0,.1), rgba(0,0,0,0)),
        radial-gradient(farthest-side at 50% 100%, rgba(0,0,0,.1), rgba(0,0,0,0)) 0 100%;
    background-repeat: no-repeat;
    background-color: white;
    background-size: 100% 40px, 100% 40px, 100% 14px, 100% 14px;
    background-attachment: local, local, scroll, scroll;
}

/* -------------------------------------------------------------------------- */
/* form                                      */
/* -------------------------------------------------------------------------- */
.form-label {
    padding-left: 0;
}
/* -------------------------------------------------------------------------- */
/* Misc                                     */
/* -------------------------------------------------------------------------- */
.ydcoza-w-600 {
   max-width: 600px;
}
.ydcoza-w-150 {
   width: 160px;
}
.table.table-stats > tbody > tr > td {
    padding-left: 0.25rem;
}
/* -------------------------------------------------------------------------- */
/* WeCoza Classes Plugin - QA Report File Display                            */
/* -------------------------------------------------------------------------- */
/* QA-specific styles (extending existing patterns) */
.qa-visit-row {
    border-left: 3px solid transparent;
    transition: border-color 0.2s ease;
    padding-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.qa-visit-row:hover {
    border-left-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

/* QA Report File Display Container */
.qa-report-file-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
    font-size: 0.625rem;
}

/* File Info Section */
.qa-report-file-display .file-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    min-width: 0; /* Allow text truncation */
}

/* File link styling */
.qa-report-file-display .file-info a {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    color: #495057;
    text-decoration: none;
    transition: color 0.2s ease;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.qa-report-file-display .file-info a:hover {
    color: #0056b3;
}

/* File size text */
.qa-report-file-display .file-info .text-muted {
    font-size: 0.625rem;
    white-space: nowrap;
}

/* Replace button styling */
.qa-report-file-display .btn-replace-file {
    white-space: nowrap;
    padding: 0.25rem 0.625rem;
    font-size: 0.8125rem;
    line-height: 1.3;
    flex-shrink: 0;
}

.qa-report-file-display .btn-replace-file i {
    font-size: 0.875rem;
}

/* New file preview styling */
.qa-new-file-preview {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.75rem;
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 0.25rem;
    color: #0c5460;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.qa-new-file-preview i {
    font-size: 1rem;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .qa-report-file-display {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .qa-report-file-display .file-info {
        justify-content: center;
    }
    
    .qa-report-file-display .btn-replace-file {
        width: 100%;
        justify-content: center;
    }
}

/* -------------------------------------------------------------------------- */
/* WeCoza Classes Plugin - Monthly Schedule Breakdown                        */
/* -------------------------------------------------------------------------- */

/* Month Card Hover Effects */
.wecoza-single-class-display .card.border-300:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

/* Clickable Month Header */
.wecoza-single-class-display [data-bs-toggle="collapse"] {
    transition: all 0.2s ease;
}

.wecoza-single-class-display [data-bs-toggle="collapse"]:hover {
    background-color: rgba(0,0,0,0.05);
    border-radius: 4px;
    padding: 2px 4px;
}

/* Chevron Animation */
.wecoza-single-class-display [data-bs-toggle="collapse"] .bi-chevron-down {
    transition: transform 0.2s ease;
}

.wecoza-single-class-display [data-bs-toggle="collapse"][aria-expanded="true"] .bi-chevron-down {
    transform: rotate(180deg);
}

/* Calculation Breakdown Styling */
.wecoza-single-class-display .collapse .border-top {
    border-color: rgba(0,0,0,0.1) !important;
}

.wecoza-single-class-display .fs-11 {
    font-size: 0.75rem;
    line-height: 1.3;
}
/* -------------------------------------------------------------------------- */
/* Navagation                                    */
/* -------------------------------------------------------------------------- */
.page #content,
.single-app #content {
    min-height: 100vh;
    padding: calc(var(--phoenix-navbar-top-height) + 2rem) 4rem 6.375rem 12.5rem;
    position: relative;
    padding-bottom: 6rem;
}
@media (min-width: 576px) {
    .page #content,
    .single-app #content {
        padding-bottom: 4rem;
    }
}
.navbar-brand,
.navbar-brand img {
  max-width: 160px;
  overflow: hidden;
}
.page-template-dashboard-template .content {
    padding-top: 2rem;
}
@media (min-width: 992px) {
    .page-template-dashboard-template .content {
        padding-left: 2.5rem;
        padding-right: 2.5rem;
        margin-left: 15.875rem;
        padding-top: 2rem;
    }
}
/* choose File Button Styles */
.ydcoza-upload input[type="file"]::file-selector-button {
  border: 2px solid #fff;
  padding: 0.2em 0.4em;
  border-radius: 0;
  background-color: #fff !important;
}

.ydcoza-upload input[type="file"]::file-selector-button:hover {
  background-color: #fff !important;
}

/* WebKit fallback */
.ydcoza-upload input[type="file"]::-webkit-file-upload-button {
  border: 2px solid #fff;
  padding: 0.2em 0.4em;
  border-radius: 0;
  background-color: #fff !important;
}

.ydcoza-upload input[type="file"]::-webkit-file-upload-button:hover {
  background-color: #fff !important;
}
html[data-bs-theme="dark"] .ydcoza-upload input[type="file"]::file-selector-button {
  border: 2px solid var(--phoenix-emphasis-bg);
  background-color: var(--phoenix-emphasis-bg) !important;
}
html[data-bs-theme="dark"] .ydcoza-upload input[type="file"]::file-selector-button:hover {
  background-color: var(--phoenix-emphasis-bg) !important;
}

html[data-bs-theme="dark"] .ydcoza-upload input[type="file"]::-webkit-file-upload-button {
  border: 2px solid var(--phoenix-emphasis-bg);
  background-color: var(--phoenix-emphasis-bg) !important;
}

html[data-bs-theme="dark"] .ydcoza-upload input[type="file"]::-webkit-file-upload-button:hover {
  background-color: var(--phoenix-emphasis-bg) !important;
}

html[data-bs-theme="dark"] .d-td-none{
  display:none
}
html[data-bs-theme="light"] .d-tl-none{
  display:none
}

/* QA Dashboard Styles */

/* Dashboard Layout */
.qa-analytics-dashboard {
    margin: 20px;
}

.qa-dashboard-controls {
    background: #fff;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group label {
    font-weight: 600;
    font-size: 12px;
    color: #666;
}

.control-group input,
.control-group select {
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
}

/* Metrics Summary */
.qa-metrics-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.metric-card h3 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
    margin-bottom: 5px;
}

.metric-change {
    font-size: 12px;
    color: #666;
}

.metric-change.positive {
    color: #46b450;
}

.metric-change.negative {
    color: #dc3232;
}

/* Charts Section */
.qa-charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    position: relative;
}

.chart-container h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 16px;
}

.chart-container canvas {
    max-height: 300px;
    width: 100% !important;
}

/* Recent Activity */
.qa-recent-activity,
.qa-alerts {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
}

.qa-recent-activity h3,
.qa-alerts h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.qa-recent-activity table {
    width: 100%;
    border-collapse: collapse;
}

.qa-recent-activity th,
.qa-recent-activity td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.qa-recent-activity th {
    background: #f9f9f9;
    font-weight: 600;
}

.qa-alerts .notice {
    margin-bottom: 10px;
    padding: 10px;
    border-left: 4px solid #0073aa;
    background: #f9f9f9;
}

.qa-alerts .notice.notice-warning {
    border-left-color: #f56e28;
}

.qa-alerts .notice.notice-error {
    border-left-color: #dc3232;
}

.qa-alerts .notice.notice-success {
    border-left-color: #46b450;
}

/* Widget Styles */
.qa-dashboard-widget {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    overflow: hidden;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    background: #f9f9f9;
}

.widget-header h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.widget-controls {
    display: flex;
    gap: 10px;
}

.widget-controls .button {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    line-height: 1.4;
    border-radius: 3px;
}

.widget-metrics {
    display: flex;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    gap: 20px;
}

.widget-metric {
    flex: 1;
    text-align: center;
}

.metric-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.widget-chart {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.widget-chart h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #333;
}

.widget-chart canvas {
    height: 120px !important;
    width: 100% !important;
}

.widget-recent-visits,
.widget-alerts {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.widget-recent-visits h4,
.widget-alerts h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
}

.widget-visits-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.visit-item {
    display: flex;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.visit-item:last-child {
    border-bottom: none;
}

.visit-date {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    min-width: 80px;
}

.visit-details {
    flex: 1;
}

.visit-details strong {
    color: #0073aa;
}

.visit-notes {
    font-size: 12px;
    color: #666;
    margin-top: 3px;
}

.widget-alerts-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    font-size: 13px;
}

.alert-icon {
    width: 16px;
    height: 16px;
    font-size: 16px;
}

.alert-warning .alert-icon {
    color: #f56e28;
}

.alert-error .alert-icon {
    color: #dc3232;
}

.alert-success .alert-icon {
    color: #46b450;
}

.alert-info .alert-icon {
    color: #0073aa;
}

.widget-actions {
    padding: 15px 20px;
    display: flex;
    gap: 10px;
}

.widget-actions .button {
    flex: 1;
    text-align: center;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .qa-charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .qa-dashboard-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .qa-metrics-summary {
        grid-template-columns: 1fr;
    }
    
    .widget-metrics {
        flex-direction: column;
        gap: 10px;
    }
    
    .widget-actions {
        flex-direction: column;
    }
    
    .visit-item {
        flex-direction: column;
        gap: 5px;
    }
    
    .widget-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .widget-controls {
        justify-content: center;
    }
}

.menu-item,
.nav-item {
  position: relative;
}
.menu-item.nav-item i._mi {
  color: var(--phoenix-navbar-vertical-link-hover-color);
    width: 16px;
    height: 16px;
    margin-top: -.265em;
    font-size: 1.4em;
    line-height: 20px;
    display: inline-block;
}

li.nav-item i._mi {
  color: var(--phoenix-navbar-vertical-link-hover-color);
    width: 23px;
    height: 16px;
    margin-top: -.25em;
    font-size: 1.4em;
    line-height: 20px;
    display: inline-block;
}

/* Smooth rotation transition for the arrow icon */
.dropdown-indicator-icon {
    position: absolute;
    top: 9px;
    left: 25px;
    color: var(--phoenix-navbar-vertical-dropdown-indicator-color);
    transition: transform 0.2s ease;
}
/* Rotate the caret icon 90deg (pointing down) when the submenu is expanded */
.nav-link[aria-expanded="true"] .dropdown-indicator-icon {
  transform: rotate(90deg);
}

/* Make the icon+text container a flex row and center them vertically */
.nav-link-text {
  display: inline-flex;      /* or just `flex` if block-level is OK */
  align-items: center;
  gap: 0.5em;                 /* space between icon and text */
}

/* (Optional) If you need finer control on the icon itself: */
.nav-link-text i {
  display: inline-block;     /* ensures vertical-align rules apply */
  vertical-align: middle;    /* fallback for non-flex environments */
  /* margin-right instead of gap if you prefer:
  margin-right: 0.5em;
  */
}

li.nav-item.active a {
  color: var(--phoenix-navbar-vertical-link-active-color) !important;
}
.page .entry-header  h1, .h1 {
        font-size: 1.8rem;
        margin-bottom: 2rem;
}
.page aside#secondary {
  display: none;
}

body .bg-body-tertiary {
    background-color: transparent !important;
}

.entry-title {
  font-size: 2rem;
  margin-bottom: 2rem;
}

.navbar-vertical-footer .support-chat + .btn-support-chat {
  bottom: 2.5rem;
  left: 3rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  margin-left: -0.5rem;
}

.breadcrumb a,
.breadcrumb .current {
  margin: 0 0.5rem;
}
.bd-callout.bd-callout-info {
  font-size: 0.875rem;
}
.support-chat .card-body {
    height: 24rem
}
.ping-icon-wrapper .ping-icon-bg {
    left: 1px;
}
html[data-bs-theme="dark"] .border-success-subtle {
    border-color: #408650 !important;
}
html[data-bs-theme="dark"] .fc.fc-theme-standard .fc-list,
html[data-bs-theme="dark"] .fc.fc-theme-standard td,
html[data-bs-theme="dark"] .fc.fc-theme-standard th {
    border: 1px solid #222834;
}
html[data-bs-theme="dark"] .fc .fc-multimonth {
    border: 1px solid #566485;
}
html[data-bs-theme="dark"] .navbar-top {
  padding-top: 0.4rem;
}

/* -------------------------------------------------------------------------- */
/* Table                                      */
/* -------------------------------------------------------------------------- */
.table {
  vertical-align: middle;
}
/* .table-sm > :not(caption) > * > * {
    padding: 0 0.25rem;
} */
.wecoza-classes-display .dropdown-toggle::after {
  display: none;
}
.wecoza-classes-display .dropdown-toggle.show,
.wecoza-classes-display .dropdown-toggle:hover,
#agents-display-data .dropdown-toggle.show,
#agents-display-data .dropdown-toggle:hover {
  color: rgba(var(--phoenix-danger-rgb), var(--phoenix-text-opacity)) !important;
}
#agents-display-data .dropdown-toggle::after {
  display: none;
}
/* More specific selector */
.w-100.table-stats.table tbody tr:last-child td {
  border-bottom: none !important;
}

/* -------------------------------------------------------------------------- */
/* Class Schedule Form Styles                                      */
/* -------------------------------------------------------------------------- */
.days-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.form-check-inline {
    margin-right: 0;
}
#day-selection-container .invalid-feedback,
#day-selection-container .valid-feedback {
    display: block;
    margin-top: 0.5rem;
}

.ydcoza-table-subheader {
    background-color: var(--phoenix-emphasis-bg);
}
.ydcoza-table-subheader th {
    padding-left: 0.5rem;
}

/* -------------------------------------------------------------------------- */
/* Calendar                                     */
/* -------------------------------------------------------------------------- */
/* Public Holiday Styles */
.wecoza-public-holiday {
    opacity: 1;
    pointer-events: none; /* Make non-interactive */
}

.fc-event.wecoza-public-holiday {
    background-color: #dc3545 !important; /* Bootstrap danger color */
    border-color: #dc3545 !important;
    color: #ffffff !important;
    font-weight: 600;
    font-size: 0.75rem;
    text-align: center;
    cursor: default;
    font-style: normal;
}

.fc-event.wecoza-public-holiday:hover {
    background-color: #c82333 !important; /* Darker red on hover */
    border-color: #c82333 !important;
    transform: none; /* Disable hover transform */
}

/* Public holiday day number styling */
.fc-day.fc-day-today.wecoza-public-holiday-day .fc-daygrid-day-number {
    background-color: #dc3545;
    color: #ffffff;
}

  .fc .fc-daygrid-event {
    font-size: 0.65rem;
  }

/**
 * WeCoza Calendar Styles
 * Custom styles for FullCalendar integration in WeCoza Classes Plugin
 */

/* Calendar Legend */
.calendar-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

.legend-color.class-event {
    background-color: #0d6efd;
}

.legend-color.public-holiday {
    background-color: #fa7272;
}

.legend-color.exception {
    background-color: #ff9800;
}

.legend-color.stop-restart {
    background-color: #f44336;
}
.legend-color.stop-restart-restart {
    background-color: var(--phoenix-success);
}
.legend-color.stop-period {
    background-color: #f44336;
    border-radius: 50%;
    width: 8px;
    height: 8px;
}
.fc .wecoza-exception-event.fc-bg-event {
    background-color: var(--phoenix-warning-border-subtle);;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    #classCalendar {
        background: #212529;
        color: #fff;
    }
    
    .fc-header-toolbar {
        background: #343a40;
        border-bottom-color: #495057;
    }
    
    .fc-toolbar-title {
        color: #fff;
    }
    
    .fc-daygrid-day {
        background: #212529;
        border-color: #495057;
    }
    
    .fc-daygrid-day:hover {
        background: #343a40;
    }
    
    .calendar-legend {
        background: #343a40;
        color: #fff;
    }
}

/* Animation for Event Loading */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fc-event {
    animation: fadeIn 0.3s ease-in-out;
}

/* Tooltip Styling */
.fc-event[title] {
    cursor: pointer;
}

/* Print Styles */
@media print {
    #classCalendar {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .fc-button {
        display: none;
    }
    
    .calendar-legend {
        page-break-inside: avoid;
    }
}


.fc .text-danger.wecoza-restart.fc-daygrid-event .fc-event-main {
    color: var(--phoenix-success) !important;
}
.fc .text-danger.wecoza-restart.fc-daygrid-event .fc-event-main::after {
    border: 4px solid var(--phoenix-success);
}
/* -------------------------------------------------------------------------- */
/* Shortcode List                                */
/* -------------------------------------------------------------------------- */
.wecoza-shortcode-list .category-section:not(:last-child) {
    border-bottom: 1px solid var(--phoenix-border-color);
}

.wecoza-shortcode-list code {
    font-size: 0.8rem;
    word-break: break-all;
}

.wecoza-shortcode-list .badge {
    font-size: 0.65rem;
    font-weight: 500;
}

.wecoza-shortcode-list .table td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

.wecoza-shortcode-list .table th {
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.75rem 0.5rem;
}

/* -------------------------------------------------------------------------- */
/* WeCoza Classes Plugin v4.0 - Calendar/List View Toggle Feature             */
/* Implementation Date: 2025-06-17                                             */
/* -------------------------------------------------------------------------- */

/* View Toggle Navigation Tabs */
#scheduleViewTabs {
    border-bottom: 2px solid var(--phoenix-border-color);
    margin-bottom: 1.5rem;
}

#scheduleViewTabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    background: none;
    color: var(--phoenix-body-color);
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

#scheduleViewTabs .nav-link:hover {
    border-bottom-color: var(--phoenix-primary);
    color: var(--phoenix-primary);
    background: rgba(var(--phoenix-primary-rgb), 0.05);
}

#scheduleViewTabs .nav-link.active {
    border-bottom-color: var(--phoenix-primary);
    color: var(--phoenix-primary);
    background: rgba(var(--phoenix-primary-rgb), 0.1);
    font-weight: 600;
}

#scheduleViewTabs .nav-link i {
    font-size: 0.9rem;
}

/* List View Styling */
#classScheduleList {
    min-height: 400px;
}

#classScheduleList .card {
    transition: box-shadow 0.3s ease;
}

#classScheduleList .card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
}

/* Event Group Headers */
#classScheduleList .card-header {
    background: var(--phoenix-emphasis-bg) !important;
    border-bottom: 1px solid var(--phoenix-border-color);
}

#classScheduleList .card-header h6 {
    color: var(--phoenix-body-color);
    font-size: 0.875rem;
    font-weight: 600;
}

/* Event List Table Styling */
#classScheduleList .table {
    font-size: 0.875rem;
}

#classScheduleList .table th {
    background: var(--phoenix-tertiary-bg);
    color: var(--phoenix-body-color);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.75rem;
}

#classScheduleList .table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--phoenix-border-color);
}

#classScheduleList .table tbody tr:hover {
    background: var(--phoenix-tertiary-bg);
}

/* Event Type Badges */
#classScheduleList .badge {
    font-size: 0.7rem;
    font-weight: 500;
    padding: 0.35rem 0.65rem;
}

/* Loading and Error States */
#list-loading, #list-error, #list-empty {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

#list-empty i {
    font-size: 3rem;
    color: var(--phoenix-tertiary-color);
    margin-bottom: 1rem;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    #scheduleViewTabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    #scheduleViewTabs .nav-link i {
        font-size: 0.8rem;
    }

    #classScheduleList .table {
        font-size: 0.8rem;
    }

    #classScheduleList .table th,
    #classScheduleList .table td {
        padding: 0.5rem;
    }

    #classScheduleList .card-header h6 {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    #scheduleViewTabs {
        margin-bottom: 1rem;
    }

    #scheduleViewTabs .nav-link {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    /* Stack event details vertically on mobile */
    #classScheduleList .table td .d-flex {
        flex-direction: column !important;
        align-items: flex-start !important;
    }

    #classScheduleList .table td .d-flex > * {
        margin-bottom: 0.25rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    #scheduleViewTabs {
        border-bottom-color: #495057;
    }

    #scheduleViewTabs .nav-link {
        color: #adb5bd;
    }

    #scheduleViewTabs .nav-link:hover,
    #scheduleViewTabs .nav-link.active {
        color: #fff;
        background: rgba(13, 110, 253, 0.1);
    }

    #classScheduleList .card {
        background: #212529;
        border-color: #495057;
    }

    #classScheduleList .card-header {
        background: #343a40 !important;
        border-bottom-color: #495057;
    }

    #classScheduleList .table th {
        background: #343a40;
        color: #fff;
    }

    #classScheduleList .table td {
        border-bottom-color: #495057;
        color: #adb5bd;
    }

    #classScheduleList .table tbody tr:hover {
        background: #343a40;
    }
}

/* Animation for View Switching */
.tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print Styles for List View */
@media print {
    #scheduleViewTabs {
        display: none;
    }

    #classScheduleList .card {
        box-shadow: none;
        border: 1px solid #000;
        page-break-inside: avoid;
    }

    #classScheduleList .card-header {
        background: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    #classScheduleList .table {
        font-size: 0.75rem;
    }
}

/* -------------------------------------------------------------------------- */
/* WeCoza Classes Plugin v4.0 - Per-Day Time Controls Styling (Tasks 5.1-5.5) */
/* Implementation Date: 2025-01-09                                             */
/* -------------------------------------------------------------------------- */

/* Task 5.1: Per-Day Time Control Layout Styles */

#per-day-time-controls.active {
    border-color: var(--phoenix-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--phoenix-primary-rgb), 0.25);
}

/* Individual Day Time Section Cards */
.per-day-time-section {
    position: relative;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(10px);
    animation: slideInUp 0.4s ease forwards;
}

.per-day-time-section:nth-child(1) { animation-delay: 0.1s; }
.per-day-time-section:nth-child(2) { animation-delay: 0.2s; }
.per-day-time-section:nth-child(3) { animation-delay: 0.3s; }
.per-day-time-section:nth-child(4) { animation-delay: 0.4s; }
.per-day-time-section:nth-child(5) { animation-delay: 0.5s; }
.per-day-time-section:nth-child(6) { animation-delay: 0.6s; }
.per-day-time-section:nth-child(7) { animation-delay: 0.7s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.per-day-time-section .card {
    border: 2px solid var(--phoenix-border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.per-day-time-section .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--phoenix-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.per-day-time-section:hover .card,
.per-day-time-section:focus-within .card {
    border-color: var(--phoenix-primary);
    box-shadow: 0 4px 12px rgba(var(--phoenix-primary-rgb), 0.15);
    transform: translateY(-2px);
}

.per-day-time-section:hover .card::before,
.per-day-time-section:focus-within .card::before {
    opacity: 1;
}

/* Day Time Header Styling */
.day-time-header {
    background: linear-gradient(135deg, var(--phoenix-emphasis-bg) 0%, var(--phoenix-body-bg) 100%);
    border-bottom: 1px solid var(--phoenix-border-color);
    padding: 0.75rem 1rem;
    position: relative;
}

.day-time-header .day-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--phoenix-emphasis-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.day-time-header .day-name::before {
    content: '📅';
    font-size: 0.875rem;
    opacity: 0.7;
}

/* Copy to All Button Styling */
.copy-to-all-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.copy-to-all-btn:hover {
    background-color: var(--phoenix-primary);
    border-color: var(--phoenix-primary);
    color: white;
    transform: scale(1.05);
}

.copy-to-all-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--phoenix-primary-rgb), 0.5);
}

/* Card Body Styling */
/* .per-day-time-section .card-body {
    padding: 1rem;
    background: var(--phoenix-body-bg);
} */

/* Time Input Controls */
.per-day-time-section .form-floating {
    position: relative;
}

.per-day-time-section .form-select {
    border: 1px solid var(--phoenix-border-color);
    border-radius: 6px;
    transition: all 0.3s ease;
    background-color: var(--phoenix-body-bg);
}

.per-day-time-section .form-select:focus {
    border-color: var(--phoenix-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--phoenix-primary-rgb), 0.25);
    background-color: var(--phoenix-body-bg);
}

.per-day-time-section .form-select:valid {
    border-color: var(--phoenix-success);
}

.per-day-time-section .form-select:invalid {
    border-color: var(--phoenix-danger);
}

/* Duration Display */
.day-duration-display {
    font-size: 0.8rem;
    color: var(--phoenix-secondary-color);
    font-weight: 500;
    padding: 0.25rem 0;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}
.day-duration-display::before {
    content: '⏱️';
    font-size: 0.75rem;
}

/* .duration-value {
    font-weight: 600;
    color: var(--phoenix-warning-text-emphasis);
} */


/* Mobile-First Approach */
@media (max-width: 575.98px) {
    #per-day-time-controls {
        margin: 1rem -0.5rem 0;
        padding: 0.75rem;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    /* Bootstrap handles spacing with g-4 class */

    .per-day-time-section .card {
        border-radius: 6px;
    }

    .day-time-header {
        padding: 0.5rem 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .day-time-header .d-flex {
        width: 100%;
        justify-content: space-between;
        align-items: center;
    }

    .copy-to-all-btn {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .per-day-time-section .card-body {
        padding: 0.75rem;
    }

    .per-day-time-section .row {
        margin: 0;
    }

    .per-day-time-section .col-md-6 {
        padding: 0 0.25rem;
        margin-bottom: 0.75rem;
    }

    /* Stack time inputs vertically on very small screens */
    .per-day-time-section .form-select {
        font-size: 0.9rem;
        min-height: 48px; /* Touch-friendly minimum */
    }

    .per-day-time-section .form-floating label {
        font-size: 0.85rem;
    }
}

/* Tablet Styles */
@media (min-width: 576px) and (max-width: 991.98px) {
    /* Bootstrap grid handles layout */

    .per-day-time-section .row {
        display: flex;
        flex-wrap: wrap;
    }

    .per-day-time-section .col-md-6 {
        flex: 1;
        min-width: 200px;
    }

    .copy-to-all-btn {
        font-size: 0.75rem;
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    /* Bootstrap grid handles layout */

    .per-day-time-section .row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 1rem;
        align-items: start;
    }

    .per-day-time-section .col-md-6 {
        margin-bottom: 0;
    }

    /* Duration display gets its own column on desktop */
    .per-day-time-section .row > div:last-child {
        display: flex;
        align-items: end;
        padding-bottom: 0.5rem;
    }
}

/* Large Desktop Optimizations */
/* @media (min-width: 1200px) {
    #per-day-time-controls {
        padding: 1.5rem;
    } */

    /* Bootstrap handles spacing with g-4 class */

    /* .per-day-time-section .card-body {
        padding: 1.25rem;
    }
} */

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .copy-to-all-btn {
        min-height: 44px;
        min-width: 44px;
        padding: 0.5rem;
    }

    .per-day-time-section .form-select {
        min-height: 48px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Remove hover effects on touch devices */
    .per-day-time-section:hover .card {
        transform: none;
        box-shadow: none;
    }

    .copy-to-all-btn:hover {
        transform: none;
    }
}

/* Task 5.3: Visual Indicators for Active/Inactive Days */
/* Active Day States */
.per-day-time-section.day-active .card {
    border-color: var(--phoenix-success);
    background: linear-gradient(135deg, rgba(var(--phoenix-success-rgb), 0.05) 0%, transparent 100%);
}

.per-day-time-section.day-active .day-time-header {
    background: linear-gradient(135deg, rgba(var(--phoenix-success-rgb), 0.1) 0%, var(--phoenix-emphasis-bg) 100%);
    border-bottom-color: rgba(var(--phoenix-success-rgb), 0.2);
}

.per-day-time-section.day-active .day-name::before {
    content: '✅';
}

.per-day-time-section.day-active .card::before {
    background: var(--phoenix-success);
    opacity: 1;
}

/* Inactive Day States */
.per-day-time-section.day-inactive .card {
    border-color: var(--phoenix-border-color);
    background: rgba(var(--phoenix-secondary-bg), 0.3);
    opacity: 0.7;
}

.per-day-time-section.day-inactive .day-time-header {
    background: var(--phoenix-secondary-bg);
    color: var(--phoenix-secondary-color);
}

.per-day-time-section.day-inactive .day-name {
    color: var(--phoenix-secondary-color);
}

.per-day-time-section.day-inactive .day-name::before {
    content: '⭕';
    opacity: 0.5;
}

.per-day-time-section.day-inactive .form-select {
    background-color: var(--phoenix-secondary-bg);
    color: var(--phoenix-secondary-color);
    cursor: not-allowed;
}

.per-day-time-section.day-inactive .copy-to-all-btn {
    opacity: 0.5;
    pointer-events: none;
}

/* Validation States with Visual Indicators */
.per-day-time-section.has-error .card {
    border-color: var(--phoenix-danger);
    animation: shake 0.5s ease-in-out;
}

.per-day-time-section.has-error .day-name::after {
    content: '⚠️';
    margin-left: 0.5rem;
    animation: pulse 1s infinite;
}

.per-day-time-section.has-success .card {
    border-color: var(--phoenix-success);
}

.per-day-time-section.has-success .day-name::after {
    content: '✓';
    margin-left: 0.5rem;
    color: var(--phoenix-success);
    font-weight: bold;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .per-day-time-section.day-active .card {
        border-width: 3px;
        border-style: solid;
    }

    .per-day-time-section.day-inactive .card {
        border-width: 3px;
        border-style: dashed;
    }

    .per-day-time-section.has-error .card {
        border-width: 4px;
        border-style: double;
    }
}

/* Task 5.4: Accessible Tooltips and Help Text */
/* Tooltip Container */
.wecoza-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

/* Tooltip Content */
.wecoza-tooltip-content {
    visibility: hidden;
    opacity: 0;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--phoenix-dark);
    color: var(--phoenix-white);
    text-align: center;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    line-height: 1.4;
    white-space: nowrap;
    max-width: 250px;
    white-space: normal;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    pointer-events: none;
}

/* Tooltip Arrow */
.wecoza-tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--phoenix-dark) transparent transparent transparent;
}

/* Show Tooltip on Hover and Focus */
.wecoza-tooltip:hover .wecoza-tooltip-content,
.wecoza-tooltip:focus .wecoza-tooltip-content,
.wecoza-tooltip:focus-within .wecoza-tooltip-content {
    visibility: visible;
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
    pointer-events: auto;
}

/* Tooltip Positioning Variants */
.wecoza-tooltip.tooltip-top .wecoza-tooltip-content {
    bottom: 125%;
    top: auto;
}

.wecoza-tooltip.tooltip-bottom .wecoza-tooltip-content {
    top: 125%;
    bottom: auto;
}

.wecoza-tooltip.tooltip-bottom .wecoza-tooltip-content::after {
    top: -5px;
    bottom: auto;
    border-color: transparent transparent var(--phoenix-dark) transparent;
}

.wecoza-tooltip.tooltip-left .wecoza-tooltip-content {
    top: 50%;
    right: 125%;
    left: auto;
    bottom: auto;
    transform: translateY(-50%);
}

.wecoza-tooltip.tooltip-left .wecoza-tooltip-content::after {
    top: 50%;
    left: 100%;
    right: auto;
    margin-top: -5px;
    margin-left: 0;
    border-color: transparent transparent transparent var(--phoenix-dark);
}

.wecoza-tooltip.tooltip-right .wecoza-tooltip-content {
    top: 50%;
    left: 125%;
    right: auto;
    bottom: auto;
    transform: translateY(-50%);
}

.wecoza-tooltip.tooltip-right .wecoza-tooltip-content::after {
    top: 50%;
    right: 100%;
    left: auto;
    margin-top: -5px;
    margin-left: 0;
    border-color: transparent var(--phoenix-dark) transparent transparent;
}

/* Help Text Styling */
.wecoza-help-text {
    font-size: 0.8rem;
    color: var(--phoenix-secondary-color);
    margin-top: 0.25rem;
    line-height: 1.4;
    display: flex;
    align-items: flex-start;
    gap: 0.25rem;
}

.wecoza-help-text::before {
    content: 'ℹ️';
    font-size: 0.75rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.wecoza-help-text.help-warning {
    color: var(--phoenix-warning);
}

.wecoza-help-text.help-warning::before {
    content: '⚠️';
}

.wecoza-help-text.help-success {
    color: var(--phoenix-success);
}

.wecoza-help-text.help-success::before {
    content: '✅';
}

/* Keyboard Navigation Support */
.wecoza-tooltip:focus {
    outline: 2px solid var(--phoenix-primary);
    outline-offset: 2px;
}

/* Screen Reader Support */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Dark Mode Support for Tooltips */
html[data-bs-theme="dark"] .wecoza-tooltip-content {
    background-color: var(--phoenix-emphasis-bg);
    color: var(--phoenix-emphasis-color);
    border: 1px solid var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .wecoza-tooltip-content::after {
    border-top-color: var(--phoenix-emphasis-bg);
}

html[data-bs-theme="dark"] .wecoza-tooltip.tooltip-bottom .wecoza-tooltip-content::after {
    border-bottom-color: var(--phoenix-emphasis-bg);
    border-top-color: transparent;
}

html[data-bs-theme="dark"] .wecoza-tooltip.tooltip-left .wecoza-tooltip-content::after {
    border-left-color: var(--phoenix-emphasis-bg);
}

html[data-bs-theme="dark"] .wecoza-tooltip.tooltip-right .wecoza-tooltip-content::after {
    border-right-color: var(--phoenix-emphasis-bg);
}

/* -------------------------------------------------------------------------- */
/* Fontawesome                                   */
/* -------------------------------------------------------------------------- */
.bi {
    height: 1.1em;
}
/* -------------------------------------------------------------------------- */
/* Class Notes & QA Integration - Enhanced Notes System */
/* -------------------------------------------------------------------------- */

/* Notes search highlighting */
.note-search-highlight {
    background-color: #fff3cd;
    padding: 0.125rem;
    border-radius: 0.125rem;
    font-weight: 500;
}

/* Keyboard focus highlight for search results */
.highlight-result {
    background-color: #e3f2fd !important;
    border-color: #2196f3 !important;
    transition: all 0.3s ease;
    box-shadow: 0 0 0 0.125rem rgba(33, 150, 243, 0.25);
}

/* Dark mode support for search highlights */
html[data-bs-theme="dark"] .note-search-highlight {
    background-color: #664d03;
    color: #fff3cd;
}

html[data-bs-theme="dark"] .highlight-result {
    background-color: #1a237e !important;
    border-color: #3f51b5 !important;
    box-shadow: 0 0 0 0.125rem rgba(63, 81, 181, 0.25);
}

/* -------------------------------------------------------------------------- */
/* File Upload & Dropzone System - WeCoza Classes Plugin                   */
/* Implementation Date: 2025-07-11                                         */
/* -------------------------------------------------------------------------- */

/* Dropzone area styling */
.dropzone-area {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.dropzone-area:hover {
    background-color: #e9ecef;
    border-color: #6c757d !important;
}

.dropzone-area.dragover {
    background-color: #e3f2fd;
    border-color: #2196f3 !important;
}

.dropzone-area.dragover .dropzone-content {
    opacity: 0.7;
}

/* File item display and management */
.file-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    background-color: #fff;
}

.file-item:hover {
    background-color: #f8f9fa;
}

.file-item .file-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    color: #6c757d;
}

.file-item .file-info {
    flex-grow: 1;
}

.file-item .file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.file-item .file-size {
    font-size: 0.875rem;
    color: #6c757d;
}

.file-item .file-progress {
    width: 100px;
    margin: 0 1rem;
}

.file-item .file-actions {
    display: flex;
    gap: 0.5rem;
}

.file-item.uploading {
    opacity: 0.7;
}

.file-item.error {
    border-color: #dc3545;
    background-color: #f8d7da;
}

/* Dark mode support for file upload */
html[data-bs-theme="dark"] .dropzone-area {
    background-color: var(--phoenix-emphasis-bg);
}

html[data-bs-theme="dark"] .dropzone-area:hover {
    background-color: var(--phoenix-secondary-bg);
}

html[data-bs-theme="dark"] .file-item {
    background-color: var(--phoenix-body-bg);
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .file-item:hover {
    background-color: var(--phoenix-emphasis-bg);
}

/* -------------------------------------------------------------------------- */
/* Enhanced Notes System - WeCoza Classes Plugin                           */
/* Implementation Date: 2025-07-11                                         */
/* -------------------------------------------------------------------------- */

/* Note cards with priority indicators */
.note-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.note-card:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-color: #adb5bd;
}


.note-card-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* .note-card-body {
    padding: 1rem;
    font-size: small;
} */

.note-card-footer {
    padding: 0.5rem 1rem;
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
    font-size: 0.875rem;
}

.note-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}

.note-content {
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 0.75rem;
}

.note-content.note-preview {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.note-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Note category badges - Enhanced with semantic color groupings */
.note-category-badge {
    --phoenix-badge-padding-x: 0.711111em;
    --phoenix-badge-padding-y: 0.355555em;
    --phoenix-badge-font-size: 0.75em;
    --phoenix-badge-font-weight: 700;
    --phoenix-badge-color: #fff;
    --phoenix-badge-border-radius: 0.25rem;
    display: inline-block;
    padding: var(--phoenix-badge-padding-y) var(--phoenix-badge-padding-x);
    font-size: var(--phoenix-badge-font-size);
    font-weight: var(--phoenix-badge-font-weight);
    line-height: 1;
    color: var(--phoenix-badge-color);
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--phoenix-badge-border-radius);
    border: 1px solid transparent;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-left: 3px;
}


.note-category-badge:last-child {
    margin-right: 0;
}

/* Legacy badge styles - maintained for backward compatibility */
.note-category-general {
    background-color: #e9ecef;
    color: #495057;
    border-color: #dee2e6;
}

.note-category-important {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f1aeb5;
}

.note-category-reminder {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.note-category-issue {
    background-color: #f5c6cb;
    color: #721c24;
    border-color: #f1aeb5;
}

.note-category-progress {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

/* Positive/Success Category Badges (Green variants) */
.note-category-class-on-track {
    background-color: #d1f7c4;
    color: #0b5d1e;
    border-color: #9fdf9f;
}

.note-category-good-qa-report {
    background-color: #dcf8c6;
    color: #166534;
    border-color: #bbf7d0;
}

.note-category-holiday-adjustment {
    background-color: #d0f0c0;
    color: #14532d;
    border-color: #a7f3d0;
}

/* Concerning/Issue Category Badges (Red/Orange variants) */
.note-category-poor-attendance {
    background-color: #fecaca;
    color: #b91c1c;
    border-color: #fca5a5;
}

.note-category-bad-qa-report {
    background-color: #fee2e2;
    color: #dc2626;
    border-color: #fca5a5;
}

.note-category-client-unhappy {
    background-color: #fed7d7;
    color: #c53030;
    border-color: #fc8181;
}

.note-category-learners-unhappy {
    background-color: #ffedd5;
    color: #c2410c;
    border-color: #fdba74;
}

/* Operational Category Badges (Blue variants) */
.note-category-agent-absent {
    background-color: #dbeafe;
    color: #1e40af;
    border-color: #93c5fd;
}

.note-category-client-cancelled {
    background-color: #e0f2fe;
    color: #0369a1;
    border-color: #7dd3fc;
}

.note-category-weather-delay {
    background-color: #e0f7fa;
    color: #0891b2;
    border-color: #67e8f9;
}

/* Resource Problem Category Badges (Purple/Brown variants) */
.note-category-venue-issues {
    background-color: #f3e8ff;
    color: #7c3aed;
    border-color: #c4b5fd;
}

.note-category-equipment-problems {
    background-color: #fef3c7;
    color: #d97706;
    border-color: #fbbf24;
}

.note-category-material-shortage {
    background-color: #f0f4f8;
    color: #4a5568;
    border-color: #cbd5e0;
}

/* Progress Category Badges (Yellow/Amber variants) */
.note-category-learners-behind-schedule {
    background-color: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
}

.note-category-learners-too-fast {
    background-color: #fffbeb;
    color: #b45309;
    border-color: #fed7aa;
}

/* Dark mode support for enhanced badges */
html[data-bs-theme="dark"] .note-category-badge {
    box-shadow: 0 1px 2px rgba(255, 255, 255, 0.05);
}

html[data-bs-theme="dark"] .note-category-class-on-track {
    background-color: #064e3b;
    color: #6ee7b7;
    border-color: #047857;
}

html[data-bs-theme="dark"] .note-category-good-qa-report {
    background-color: #14532d;
    color: #86efac;
    border-color: #166534;
}

html[data-bs-theme="dark"] .note-category-poor-attendance {
    background-color: #7f1d1d;
    color: #fca5a5;
    border-color: #b91c1c;
}

html[data-bs-theme="dark"] .note-category-bad-qa-report {
    background-color: #991b1b;
    color: #fecaca;
    border-color: #dc2626;
}

html[data-bs-theme="dark"] .note-category-agent-absent {
    background-color: #1e3a8a;
    color: #93c5fd;
    border-color: #3730a3;
}

html[data-bs-theme="dark"] .note-category-client-cancelled {
    background-color: #0c4a6e;
    color: #7dd3fc;
    border-color: #0369a1;
}

html[data-bs-theme="dark"] .note-category-venue-issues {
    background-color: #581c87;
    color: #c4b5fd;
    border-color: #7c3aed;
}

html[data-bs-theme="dark"] .note-category-equipment-problems {
    background-color: #78350f;
    color: #fbbf24;
    border-color: #d97706;
}

/* Enhanced Notes Grid Layout */
.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.note-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.note-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #adb5bd;
}

.note-card-header {
    padding: 0.75rem 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.note-card-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    flex: 1;
}

.note-card-metadata {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Priority styling with colored left borders - FINAL VERSION */
.note-card.priority-high {
    border-left: 4px solid #dc3545 !important;
}

.note-card.priority-medium {
    border-left: 4px solid #ffc107 !important;
}

.note-card.priority-low {
    border-left: 4px solid #28a745 !important;
}

/* Priority Legend Styles */
.priority-legend {
    margin-bottom: 1rem;
}

.legend-item {
    gap: 0.5rem;
}

.legend-color-box {
    width: 20px;
    height: 12px;
    border-radius: 2px;
    display: inline-block;
}

.legend-color-box.priority-high {
    background-color: #dc3545;
}

.legend-color-box.priority-medium {
    background-color: #ffc107;
}

.legend-color-box.priority-low {
    background-color: #28a745;
}

.legend-label {
    color: #6c757d;
    font-size: 0.875rem;
}

.legend-title {
    font-weight: 500;
}

/* Compact Dropzone Styles */
.dropzone-area {
    min-height: auto !important;
    transition: border-color 0.2s ease;
}

.dropzone-area:hover {
    border-color: #0d6efd !important;
}

.dropzone-area.dragover {
    border-color: #0d6efd !important;
    background-color: rgba(13, 110, 253, 0.1);
}

/* Responsive dropzone for smaller screens */
@media (max-width: 768px) {
    .dropzone-content {
        flex-direction: column !important;
        text-align: center;
        gap: 1rem;
    }
    
    .dropzone-content .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .dropzone-content .me-3 {
        margin-right: 0 !important;
        margin-bottom: 0.5rem;
    }
}

/* QA Visit Types - reuse existing color patterns */
.qa-type-initial { color: #0d6efd; }
.qa-type-followup { color: #fd7e14; }
.qa-type-compliance { color: #6f42c1; }
.qa-type-final { color: #198754; }

.note-attachments-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6c757d;
    font-size: 0.875rem;
}

.note-card-body {
    padding: 1rem;
    padding-bottom: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.note-content-full {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057;
    margin-bottom: 0;
}

.note-content-expandable {
    position: relative;
}

.note-content-collapsed {
    max-height: 4.5em;
    overflow: hidden;
    position: relative;
}

.note-content-collapsed::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1.5em;
    background: linear-gradient(transparent, #fff);
    pointer-events: none;
}

.note-expand-btn {
    background: none;
    border: none;
    color: #0d6efd;
    cursor: pointer;
    font-size: 0.875rem;
    padding: 0.25rem 0;
    margin-top: 0.5rem;
}

.note-expand-btn:hover {
    text-decoration: underline;
}

.note-card-footer {
    padding: 0.75rem 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.note-card-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #6c757d;
}

.note-card-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.note-card-actions {
    display: flex;
    gap: 0.5rem;
}

.note-updated-indicator {
    color: #f39c12;
    font-weight: 500;
}

/* Attachment dropdown styling */
.note-attachments-dropdown {
    position: relative;
}

.note-attachments-indicator {
    border: none !important;
    background: transparent !important;
    color: #6c757d;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.note-attachments-indicator:hover,
.note-attachments-indicator:focus {
    color: #495057;
    background: rgba(0,0,0,0.05) !important;
}

.note-attachments-indicator i {
    margin-right: 0.25rem;
}

.note-attachments-dropdown .dropdown-menu {
    min-width: 200px;
    border: 1px solid #dee2e6;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
}

.note-attachments-dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.note-attachments-dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.note-attachments-dropdown .dropdown-item i {
    flex-shrink: 0;
}

/* Dark mode support for enhanced cards */
html[data-bs-theme="dark"] .note-card {
    background: var(--phoenix-body-bg);
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .note-card-header,
html[data-bs-theme="dark"] .note-card-footer {
    background-color: var(--phoenix-emphasis-bg);
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .note-content-full {
    color: var(--phoenix-body-color);
}

html[data-bs-theme="dark"] .note-content-collapsed::after {
    background: linear-gradient(transparent, var(--phoenix-body-bg));
}

html[data-bs-theme="dark"] .note-card-meta {
    color: var(--phoenix-tertiary-color);
}

html[data-bs-theme="dark"] .note-attachments-indicator {
    color: var(--phoenix-tertiary-color);
}

html[data-bs-theme="dark"] .note-attachments-indicator:hover,
html[data-bs-theme="dark"] .note-attachments-indicator:focus {
    color: var(--phoenix-body-color);
    background: var(--phoenix-emphasis-bg) !important;
}

html[data-bs-theme="dark"] .note-attachments-dropdown .dropdown-menu {
    background-color: var(--phoenix-body-bg);
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .note-attachments-dropdown .dropdown-item {
    color: var(--phoenix-body-color);
}

html[data-bs-theme="dark"] .note-attachments-dropdown .dropdown-item:hover {
    background-color: var(--phoenix-emphasis-bg);
    color: var(--phoenix-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .notes-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .note-card-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .note-card-metadata {
        justify-content: flex-end;
    }
    
    .note-card-footer {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
    
    .note-card-meta {
        justify-content: center;
    }
    
    .note-card-actions {
        justify-content: center;
    }
}

/* Note tags and attachments */
.note-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.note-tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    text-decoration: none;
}

.note-tag:hover {
    background-color: #dee2e6;
    color: #495057;
}

.note-actions {
    display: flex;
    gap: 0.5rem;
}

.note-actions .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.note-attachments {
    margin-top: 0.5rem;
}

.note-attachment {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    text-decoration: none;
    color: #495057;
}

.note-attachment:hover {
    background-color: #e9ecef;
    color: #495057;
}

.note-attachment-icon {
    width: 16px;
    height: 16px;
}

/* Notes table view */
/* .notes-table {
    width: 100%;
    margin-bottom: 1rem;
}

.notes-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    padding: 0.75rem;
    font-size: 0.875rem;
    color: #495057;
} */

/* .notes-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.notes-table tr:hover {
    background-color: #f8f9fa;
}

.notes-table .note-title-cell {
    font-weight: 600;
    color: #495057;
}

.notes-table .note-content-cell {
    max-width: 300px;
    color: #6c757d;
}

.notes-table .note-meta-cell {
    font-size: 0.875rem;
    color: #6c757d;
} */

/* View toggle buttons */
.notes-view-toggle {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

/* Notes search highlighting */
.note-search-highlight {
    background-color: #fff3cd;
    padding: 0.125rem;
    border-radius: 0.125rem;
    font-weight: 500;
}

/* Keyboard focus highlight for search results */
.highlight-result {
    background-color: #e3f2fd !important;
    border-color: #2196f3 !important;
    transition: all 0.3s ease;
    box-shadow: 0 0 0 0.125rem rgba(33, 150, 243, 0.25);
}

/* Dark mode support for notes system */
html[data-bs-theme="dark"] .note-card {
    background: var(--phoenix-body-bg);
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .note-card-header,
html[data-bs-theme="dark"] .note-card-footer {
    background-color: var(--phoenix-emphasis-bg);
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .note-title {
    color: var(--phoenix-emphasis-color);
}

html[data-bs-theme="dark"] .note-content {
    color: var(--phoenix-body-color);
}

/* html[data-bs-theme="dark"] .notes-table th {
    background-color: var(--phoenix-emphasis-bg);
    color: var(--phoenix-emphasis-color);
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .notes-table td {
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .notes-table tr:hover {
    background-color: var(--phoenix-emphasis-bg);
} */

html[data-bs-theme="dark"] .note-search-highlight {
    background-color: #664d03;
    color: #fff3cd;
}

html[data-bs-theme="dark"] .highlight-result {
    background-color: #1a237e !important;
    border-color: #3f51b5 !important;
    box-shadow: 0 0 0 0.125rem rgba(63, 81, 181, 0.25);
}

/* -------------------------------------------------------------------------- */
/* Timeline Component - Single Class Display                               */
/* Implementation Date: 2025-07-11                                         */
/* -------------------------------------------------------------------------- */

.timeline-container {
    position: relative;
    padding-left: 20px;
}

.timeline-item {
    position: relative;
}

.timeline-connector {
    position: absolute;
    left: 39px;
    top: 45px;
    width: 2px;
    height: calc(100% - 45px);
    background-color: #dee2e6;
}

.timeline-content {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Dark mode support for timeline */
html[data-bs-theme="dark"] .timeline-connector {
    background-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .timeline-content {
    background-color: var(--phoenix-emphasis-bg);
}

/* -------------------------------------------------------------------------- */
/* Admin Interface Components - Schedule Data Admin                        */
/* Implementation Date: 2025-07-11                                         */
/* -------------------------------------------------------------------------- */

/* Admin cards for data display */
.wecoza-admin-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin-bottom: 20px;
}

.wecoza-admin-card h2 {
    margin-top: 0;
    font-size: 1.5rem;
    color: #23282d;
}

.wecoza-admin-card pre {
    background: #f1f1f1;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
    font-size: 0.875rem;
    border: 1px solid #ddd;
}

/* Dark mode support for admin interface */
html[data-bs-theme="dark"] .wecoza-admin-card {
    background: var(--phoenix-body-bg);
    border-color: var(--phoenix-border-color);
}

html[data-bs-theme="dark"] .wecoza-admin-card h2 {
    color: var(--phoenix-emphasis-color);
}

html[data-bs-theme="dark"] .wecoza-admin-card pre {
    background: var(--phoenix-emphasis-bg);
    border-color: var(--phoenix-border-color);
    color: var(--phoenix-body-color);
}

/* -------------------------------------------------------------------------- */
/* Chat                                  */
/* -------------------------------------------------------------------------- */
.openai-response {
    background: var(--phoenix-secondary-bg);
    border-radius: 6px;
}

/* option:disabled {
   background: #ccc;
   padding: 5px;
} */

option:disabled {
    --phoenix-badge-bg: var(--phoenix-warning-bg-subtle);
    --phoenix-badge-color: var(--phoenix-warning-text-emphasis);
    --phoenix-badge-border-color: var(--phoenix-warning-border-subtle);
    padding: 2px;
    background-color: var(--phoenix-badge-bg);
    color: var(--phoenix-badge-color);
    border: 1px solid var(--phoenix-badge-border-color);
}

.btn-subtle-primary {
  --phoenix-btn-color: #003cc7;
  --phoenix-btn-bg: #e5edff;
  --phoenix-btn-border-color: #e5edff;
  --phoenix-btn-hover-color: #003cc7;
  --phoenix-btn-hover-bg: var(--phoenix-primary-lighter);
  --phoenix-btn-hover-border-color: #e8efff;
  --phoenix-btn-focus-shadow-rgb: 195, 210, 247;
  --phoenix-btn-active-color: #000000;
  --phoenix-btn-active-bg: #eaf1ff;
  --phoenix-btn-active-border-color: #e8efff;
  --phoenix-btn-active-shadow: initial;
  --phoenix-btn-disabled-color: #000000;
  --phoenix-btn-disabled-bg: #e5edff;
  --phoenix-btn-disabled-border-color: #e5edff;
}

.btn-subtle-secondary {
  --phoenix-btn-color: #525b75;
  --phoenix-btn-bg: #eff2f6;
  --phoenix-btn-border-color: #eff2f6;
  --phoenix-btn-hover-color: #525b75;
  --phoenix-btn-hover-bg: var(--phoenix-secondary-lighter);
  --phoenix-btn-hover-border-color: #f1f3f7;
  --phoenix-btn-focus-shadow-rgb: 215, 219, 227;
  --phoenix-btn-active-color: #000000;
  --phoenix-btn-active-bg: #f2f5f8;
  --phoenix-btn-active-border-color: #f1f3f7;
  --phoenix-btn-active-shadow: initial;
  --phoenix-btn-disabled-color: #000000;
  --phoenix-btn-disabled-bg: #eff2f6;
  --phoenix-btn-disabled-border-color: #eff2f6;
}

.btn-subtle-success {
  --phoenix-btn-color: #1c6c09;
  --phoenix-btn-bg: #d9fbd0;
  --phoenix-btn-border-color: #d9fbd0;
  --phoenix-btn-hover-color: #1c6c09;
  --phoenix-btn-hover-bg: var(--phoenix-success-lighter);
  --phoenix-btn-hover-border-color: #ddfbd5;
  --phoenix-btn-focus-shadow-rgb: 189, 230, 178;
  --phoenix-btn-active-color: #000000;
  --phoenix-btn-active-bg: #e1fcd9;
  --phoenix-btn-active-border-color: #ddfbd5;
  --phoenix-btn-active-shadow: initial;
  --phoenix-btn-disabled-color: #000000;
  --phoenix-btn-disabled-bg: #d9fbd0;
  --phoenix-btn-disabled-border-color: #d9fbd0;
}

.btn-subtle-info {
  --phoenix-btn-color: #005585;
  --phoenix-btn-bg: #c7ebff;
  --phoenix-btn-border-color: #c7ebff;
  --phoenix-btn-hover-color: #005585;
  --phoenix-btn-hover-bg: var(--phoenix-info-lighter);
  --phoenix-btn-hover-border-color: #cdedff;
  --phoenix-btn-focus-shadow-rgb: 169, 213, 237;
  --phoenix-btn-active-color: #000000;
  --phoenix-btn-active-bg: #d2efff;
  --phoenix-btn-active-border-color: #cdedff;
  --phoenix-btn-active-shadow: initial;
  --phoenix-btn-disabled-color: #000000;
  --phoenix-btn-disabled-bg: #c7ebff;
  --phoenix-btn-disabled-border-color: #c7ebff;
}

.btn-subtle-warning {
  --phoenix-btn-color: #bc3803;
  --phoenix-btn-bg: #ffefca;
  --phoenix-btn-border-color: #ffefca;
  --phoenix-btn-hover-color: #bc3803;
  --phoenix-btn-hover-bg: var(--phoenix-warning-lighter);
  --phoenix-btn-hover-border-color: #fff1cf;
  --phoenix-btn-focus-shadow-rgb: 245, 212, 172;
  --phoenix-btn-active-color: #000000;
  --phoenix-btn-active-bg: #fff2d5;
  --phoenix-btn-active-border-color: #fff1cf;
  --phoenix-btn-active-shadow: initial;
  --phoenix-btn-disabled-color: #000000;
  --phoenix-btn-disabled-bg: #ffefca;
  --phoenix-btn-disabled-border-color: #ffefca;
}

.btn-subtle-danger {
  --phoenix-btn-color: #b81800;
  --phoenix-btn-bg: #ffe0db;
  --phoenix-btn-border-color: #ffe0db;
  --phoenix-btn-hover-color: #b81800;
  --phoenix-btn-hover-bg: var(--phoenix-danger-lighter);
  --phoenix-btn-hover-border-color: #ffe3df;
  --phoenix-btn-focus-shadow-rgb: 244, 194, 186;
  --phoenix-btn-active-color: #000000;
  --phoenix-btn-active-bg: #ffe6e2;
  --phoenix-btn-active-border-color: #ffe3df;
  --phoenix-btn-active-shadow: initial;
  --phoenix-btn-disabled-color: #000000;
  --phoenix-btn-disabled-bg: #ffe0db;
  --phoenix-btn-disabled-border-color: #ffe0db;
}

/* Agent Capture Form Enhancements */
#google_address_search {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
}

#google_address_search:focus {
    background-color: #fff;
    border-color: #80bdff;
}

/* Google Places Autocomplete Dropdown */
.pac-container {
    z-index: 10000;
    font-family: Arial, sans-serif;
    border-radius: 0 0 5px 5px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.pac-item {
    padding: 10px;
    font-size: 14px;
    cursor: pointer;
}

.pac-item:hover {
    background-color: #f5f5f5;
}

.pac-item-selected {
    background-color: #e6e6e6;
}

/* Initials field styling */
#initials,
#class_duration,
#schedule_end_date {
    background-color: #e9ecef;
    text-align: center;
    letter-spacing: 1px;
}

#google_address_container .form-control-sm {
    min-height: calc(1.49em + 0.75rem + calc(var(--phoenix-border-width) * 2));
    padding: 0;
    font-size: 0.8rem;
    border-radius: 0.375rem;
}

#agents-display-data thead tr > th:first-child,
#agents-display-data tbody tr > td:first-child {
  padding-left: 0.5rem;
}
#agents-display-data .sort a{
  color: rgba(var(--phoenix-body-color-rgb), var(--phoenix-text-opacity)) !important;
}

.ydcoza-timeline-vertical .timeline-item::before {
    display: none;
}
.ydcoza-timeline-vertical .timeline-item {
    position: relative;
    margin-bottom: 0;
    padding-left: 0;
}

.ydcoza-mini-card-header {
    padding: 0.6rem 1rem 0.3rem;
}
.ydcoza-mini-card-header p {
    line-height: 1;
    font-size: 0.8rem;
}
.ydcoza-mini-card-header h5 {
    line-height: 1;
    font-size: 0.8rem;
}
.ydcoza-mini-card-header .rounded {
    width: 28px;
    height: 28px;
    line-height: 1;
}
.ydcoza-mini-card-header .ps-sm-5 {
    padding-left: 1rem !important;
}